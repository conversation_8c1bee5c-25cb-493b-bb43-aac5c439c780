// e2e/utils/setupDatabase.ts
import { config } from 'dotenv';
import { db, connectToDatabase, disconnectFromDatabase } from './database';
import { testDataService } from '../services/testDataService';

// Load environment variables
config();

export class DatabaseSetup {
  private static isInitialized = false;

  public static async initialize(): Promise<void> {
    if (DatabaseSetup.isInitialized) {
      return;
    }

    try {
      console.log('🔧 Initializing database connection for tests...');
      
      // Test database connection
      await connectToDatabase();
      const isConnected = await db.testConnection();
      
      if (!isConnected) {
        throw new Error('Database connection test failed');
      }

      // Initialize test data service
      await testDataService.initialize();
      
      console.log('✅ Database setup completed successfully');
      DatabaseSetup.isInitialized = true;
    } catch (error) {
      console.error('❌ Database setup failed:', error);
      throw error;
    }
  }

  public static async cleanup(): Promise<void> {
    try {
      console.log('🧹 Cleaning up database connections...');
      await disconnectFromDatabase();
      DatabaseSetup.isInitialized = false;
      console.log('✅ Database cleanup completed');
    } catch (error) {
      console.warn('⚠️ Database cleanup warning:', error);
    }
  }

  public static async validateTestData(): Promise<boolean> {
    try {
      console.log('🔍 Validating test data availability...');
      
      // Check if we can retrieve basic test data
      const products = await testDataService.getProductData(1);
      const vendors = await testDataService.getVendorData(1);
      const categories = await testDataService.getContractCategoryData(1);
      
      const hasProducts = products.length > 0;
      const hasVendors = vendors.length > 0;
      const hasCategories = categories.length > 0;
      
      if (!hasProducts) {
        console.warn('⚠️ No product data found in database');
      }
      
      if (!hasVendors) {
        console.warn('⚠️ No vendor data found in database');
      }
      
      if (!hasCategories) {
        console.warn('⚠️ No contract category data found in database');
      }
      
      const isValid = hasProducts && hasVendors && hasCategories;
      
      if (isValid) {
        console.log('✅ Test data validation passed');
      } else {
        console.error('❌ Test data validation failed');
      }
      
      return isValid;
    } catch (error) {
      console.error('❌ Test data validation error:', error);
      return false;
    }
  }

  public static async getConnectionInfo(): Promise<{
    isConnected: boolean;
    environment: string;
    cacheStats: { size: number; keys: string[] };
  }> {
    try {
      const isConnected = await db.testConnection();
      const environment = process.env.TEST_ENVIRONMENT || 'unknown';
      const cacheStats = testDataService.getCacheStats();
      
      return {
        isConnected,
        environment,
        cacheStats
      };
    } catch (error) {
      return {
        isConnected: false,
        environment: process.env.TEST_ENVIRONMENT || 'unknown',
        cacheStats: { size: 0, keys: [] }
      };
    }
  }

  public static clearCache(): void {
    testDataService.clearCache();
    console.log('🗑️ Test data cache cleared');
  }
}

// Utility function to run database setup script
export async function setupTestDatabase(): Promise<void> {
  await DatabaseSetup.initialize();
}

// Utility function to validate test environment
export async function validateTestEnvironment(): Promise<boolean> {
  try {
    await DatabaseSetup.initialize();
    return await DatabaseSetup.validateTestData();
  } catch (error) {
    console.error('❌ Test environment validation failed:', error);
    return false;
  }
}

// Utility function for test cleanup
export async function cleanupTestDatabase(): Promise<void> {
  await DatabaseSetup.cleanup();
}

// DatabaseSetup is already exported above with the class declaration
